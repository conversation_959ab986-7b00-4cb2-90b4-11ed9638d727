 function [ew_coef,pos_dat,fdc_d] = ew_coef_comp(ew_data,PRF,N_pluse)
%UNTITLED 此处显示有关此函数的摘要
%   此处显示详细说明
    PRT=1/PRF;
    Rcs_o=ew_data(:,5);
    fd3dB=ew_data(:,7);
    len_sc=length(Rcs_o);
    spectrum_way=1;
    f3dB_r=PRF/6;
    Num_r=len_sc;
    Num_t=N_pluse;
    mu=Rcs_o;
    Pf_r=Spectrun_gen(f3dB_r,PRF,Num_r,spectrum_way);
%     T=(1:Num_t)*PRT;
    %相关系数计算
    rhoij_r = rho_comp( Num_r,Pf_r);
    %产生瑞利分布杂波
    for i=1:Num_r
        Pf=Spectrun_gen(fd3dB(i),PRF,Num_t,spectrum_way);
        rhoij_t = rho_comp( Num_t,Pf);
        freq_filter=fft(rhoij_t);
        xt_I=randn(1,Num_t);
        xt_Q=randn(1,Num_t);
        xt_I=ifft(fft(xt_I).*freq_filter);
        xt_Q=ifft(fft(xt_Q).*freq_filter);
        data=xt_I+1i*xt_Q;
        amp_data=abs(data);
        Mea=mean(amp_data);
        ratio=mu(i)/Mea;
        relay_d(:,i)=data*ratio;    
    end

    %距离维相关性
    freq_filter1=fft(rhoij_r);
    for i=1:Num_t
        temp=relay_d(i,:);
        temp11=ifft(fft(temp).*freq_filter1);
        relay_data0(i,:)=temp11;    
    end

    ew_coef=relay_data0;
    pos_dat=ew_data(:,2:4);
    fdc_d=ew_data(:,6);
    % 
    
 end

