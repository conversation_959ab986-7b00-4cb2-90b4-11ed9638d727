%计算箔条云团二维分布,供成像等验证使用
clc;
close all;
clear ;
f0=17e9;
real_N=100000;%百根
kuosan_ratio=10;%该因子用于增大箔条云团所占的空域范围
F_ratio=0.5;%频谱宽度扩散因子
v_f=[10 0 0];
t1=1;%扩散阶段时间
t2=3;%稳定时间
t3=2;%消散阶段时间
v0x=10;%出口速度
v0y=0;
v0z=0;

% x0=38555.99;%箔条初始位置
% y0=0;
% z0=3855.59;
x0=0;%箔条初始位置
y0=0;
z0=0;
r0=0.5;
chaff_para=[t1 t2 t3 v0x v0y v0z x0 y0 z0 r0];
deltat=0.01;%仿真时间间隔
N_comp=1;%散射点计算数目
data_chaff = one_chaff_paracomp( f0, real_N,kuosan_ratio,F_ratio,v_f,chaff_para,deltat,N_comp);%单箔条弹数据计算


% 
t0=1.01;%计算对应成像时刻箔条状态
dr0=2;
ew_data = ew_data_comp(data_chaff,t0,dr0);

%生成二维分布数据
ew_data1=[];
n=length(data_chaff);
ew_coef1=[];
ew_coef2=[];
N_pluse=128;
PRF=100;
for i=1:n
t0(i)=data_chaff(i,1);%计算对应成像时刻箔条状态
dr0=10;
ew_data = ew_data_comp(data_chaff,t0(i),dr0);
ew_data1=[ew_data1;ew_data];
end







