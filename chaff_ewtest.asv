%计算箔条云团二维分布,供成像等验证使用
clc;
close all;
clear ;
f0=17e9;
real_N=100000;%百根
kuosan_ratio=10;%该因子用于增大箔条云团所占的空域范围
F_ratio=0.5;%频谱宽度扩散因子
v_f=[10 0 0];
t1=1;%扩散阶段时间
t2=3;%稳定时间
t3=2;%消散阶段时间
v0x=10;%出口速度
v0y=0;
v0z=0;

% x0=38555.99;%箔条初始位置
% y0=0;
% z0=3855.59;
x0=0;%箔条初始位置
y0=0;
z0=0;
r0=0.5;
chaff_para=[t1 t2 t3 v0x v0y v0z x0 y0 z0 r0];
deltat=0.01;%仿真时间间隔
N_comp=1;%散射点计算数目
data_chaff = one_chaff_paracomp( f0, real_N,kuosan_ratio,F_ratio,v_f,chaff_para,deltat,N_comp);%单箔条弹数据计算


% 
% t0=1.01;%计算对应成像时刻箔条状态
% dr0=5;
% ew_data = ew_data_comp(data_chaff,t0,dr0);

%生成二维分布数据
ew_data1=[];
n=length(data_chaff);
ew_coef1=[];
ew_coef2=[];
N_pluse=128;
PRF=100;
for i=1:n
t0(i)=data_chaff(i,1);%计算对应成像时刻箔条状态
dr0=15;
ew_data = ew_data_comp(data_chaff,t0(i),dr0);
ew_data1=[ew_data1;ew_data(:,1:6)];
[ew_coef,pos_dat,fdc_d] = ew_coef_comp(ew_data,PRF,N_pluse);
ew_coef_1=ew_coef(:,1).';
% tt2=repmat(ew_data(1,1),N_pluse,1);
% ew_coef2=[ew_data(1,1) ew_coef_1];
% bu=zeros(N_pluse,220-length(ew_coef2(1,:)));
ew_coef1=[ew_coef1;ew_coef_1];
end
% figure;
% plot(ew_data(:,6));
%二维散射特征调制系数生成
zabo_real=real(ew_coef1);
 zabo_real(1,:)=zabo_real(2,:);
zabo_imag=imag(ew_coef1);
 zabo_imag(1,:)=zabo_imag(2,:);
% zabo_imag1=[];
% zabo_imag1=[zabo_real(:,1) zabo_imag(:,2:129)];
zabo=[zabo_real(2:601,:);zabo_imag(2:601,:)];
zabo1=reshape(zabo',[],1);
% ew_data2=reshape(ew_data1,[],1);
ew_data2=reshape(ew_data1,[],1);


unique_values = unique(ew_data1(:, 1));

% 计算每个唯一值的个数
count_per_value = arrayfun(@(x) sum(ew_data1(:, 1) == x), unique_values);

% 创建一个新的数据矩阵
new_data = [];

% 遍历数据并插入统计信息
for i = 1:size(ew_data1, 1)
    % 检查当前行的第一列值是否是一个新的唯一值
    if i == 1 || ew_data1(i, 1) ~= ew_data1(i - 1, 1)
        % 找到当前值的出现次数
        value = ew_data1(i, 1);
        count = count_per_value(unique_values == value);
        % 在新数据中插入统计信息
        new_data = [new_data; zeros(1,1),value, count, zeros(1, 3)];
    end
    % 将当前行数据添加到新数据矩阵
    new_data = [new_data; ew_data1(i, :)];
end

fileID = fopen('Chaff1', 'w');

% 将数据按列顺序写入二进制文件
fwrite(fileID, new_data', 'float');  % 按列写入，每个元素为 'double' 类型

% 关闭文件
fclose(fileID);

% filename = sprintf('Chaff1.txt');
% fileID = fopen(filename, 'w');
% fprintf(fileID, '%.6e \n',length(ew_data1));
% for i = 1:size(ew_data1, 1)
%    fprintf(fileID, '%.2f\t%.2f\t%.2f\t%.2f\t%.2f\t%.2f\n', ew_data1(i,:));
% end
% fclose(fileID);
% 
% txt_filename = 'Chaff1.txt';   % 输入的文本文件名
% data = load(txt_filename);   % 假设文本文件是以空格或换行符分隔的数值
% 
% % 2. 写入二进制文件
% bin_filename = 'Chaff1';   % 输出的二进制文件名
% fileID = fopen(bin_filename, 'wb');  % 以写入模式打开二进制文件
% fwrite(fileID, data, 'float');      % 将数据写入文件，假设数据类型为 double
% fclose(fileID);  
% 
% filename = sprintf('Chaffdefalt.txt');
% fileID = fopen(filename, 'w');
% fprintf(fileID, '%.6e\n', zabo1);
% fclose(fileID);
% 
% txt_filename = 'Chaffdefalt.txt';   % 输入的文本文件名
% data = load(txt_filename);   % 假设文本文件是以空格或换行符分隔的数值
% 
% % 2. 写入二进制文件
% bin_filename = 'Chaffdefalt';   % 输出的二进制文件名
% fileID = fopen(bin_filename, 'wb');  % 以写入模式打开二进制文件
% fwrite(fileID, data, 'float');      % 将数据写入文件，假设数据类型为 double
% fclose(fileID);  
%调制系数，ew_coef为逐脉冲调制散射系数，增加多普勒展宽调制，
%pos_dat为距离单元各个散射点的xyz值
%fdc_d为各个距离散射单元的中心多普勒值
    
% %% 
% count=0;
% for i=1:length(ew_data1)
%     if(ew_data1(:,1)==1)
%         if(count==0)
%         index=i;
%         end
%         count=count+1;
%     end
% end




