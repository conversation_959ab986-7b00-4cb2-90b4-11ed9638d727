function rhoij = rho_comp( Num,Pf)
% %UNTITLED Summary of this function goes here
    M=Num;
    alpha=(M-1)/2;
    k1end=floor(alpha);
    k1=0:k1end;
    k2=k1end+1:M-1;
    phi_filter1=-alpha*2*pi*k1/M;
    phi_filter2=alpha*2*pi/M*(M-k2);
    phi_filter=[phi_filter1 phi_filter2];
    amp_filter=[Pf fliplr(Pf)];
    Freq_filter=amp_filter.*exp(1i*phi_filter);
    coef_filter=ifft(Freq_filter);
    coef_real=real(coef_filter);
    sij=abs(coef_real);
    rhoij=sij;
    
end

