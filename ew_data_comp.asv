  % function ew_data = ew_data_comp(data_chaff,t0,dr0)
%UNTITLED2 此处显示有关此函数的摘要
%   此处显示详细说明
    tt0=data_chaff(:,1);
    tt=tt0';
    indx=find(tt==t0);
    % indx=13
    temp=data_chaff(indx,:);
    xc=temp(2);
    yc=temp(3);
    zc=temp(4);
    Ru=temp(5);
    fdc=temp(6);
    fdzk=temp(7);
    RCS_z=temp(8);

    N0t=11;
    dr=Ru/N0t;
    if dr>dr0
        N0=ceil(Ru/dr0);
        xx=linspace(-Ru,Ru,N0);
    else
        xx=linspace(-Ru,Ru,N0t);       
    end

    sx=xx+xc;
    sy=xx+yc;
%     sz=xx+zc;
    %计算各个散射单元RCS值
    [X,Y]=meshgrid(sx,sy);
    [len1,len2]=size(X);
    for i=1:len1
        for j=1:len2
            sx0(i,j)=X(i,j)-xc;
            sy0(i,j)=Y(i,j)-yc;
            tempr(i,j)=sqrt( sx0(i,j)^2+sy0(i,j)^2);
            if tempr(i,j)>Ru
                weight0(i,j)=0;
            else
                xx0=3*tempr(i,j)/Ru;
                weight0(i,j)=exp(-xx0.^2/2);
            end                
        end        
    end
    
    wet_zh=sum(sum(weight0));
    weights=weight0/wet_zh;
    % figure;
    % mesh(X,Y,weights);
    Rcs_sc=weights*RCS_z;
    
    sxo0=reshape(X,1,[]);
    syo0=reshape(Y,1,[]);
    Rcs_sc0=reshape(Rcs_sc,1,[]);
    indd=find(Rcs_sc0>0);
    sxo=sxo0(indd);
    syo=syo0(indd);
    szo=zc+0.5*Ru*(2*rand(size(sxo))-1);
     % Rcs_sco=Rcs_sc0(indd);
     Rcs_sco(1:length(syo))=RCS_z/10;
     sxo3=sxo+50*rand(size(sxo));
    syo3=syo+50*rand(size(sxo));
    szo3=szo+50*rand(size(sxo));
    fdo=fdc*(1+0.05*(2*rand(size(sxo))-1));
    fd3dB=fdzk*(1+0.05*(2*rand(size(sxo))-1));
    % tt1=repmat(t0,length(syo),1);
    azi=2*pi*rand(1,length(syo));
    % ew_data0=[tt1';sxo3;syo3;szo3;Rcs_sco;azi;fdo;fd3dB]';
    ew_data1=[t0 sxo3 ]';
    Nm=size(ew_data0,1);
    if mod(Nm,2)==0
        ew_data=ew_data0;
    else
        ew_data=ew_data0(1:Nm-1,:);
    end
% figure;
% plot3(ew_data(:,2),ew_data(:,3),ew_data(:,4),'ro');
%% 
% sxo3=sxo+50*rand(size(sxo));
% syo3=syo+50*rand(size(sxo));
% szo3=szo+50*rand(size(sxo));
%  figure;
%  plot3(sxo3,syo3,szo3,'ro');
%  t=(0:40/length(sxo):40-40/length(sxo));
%   syo4=syo3+100* sawtooth(2*pi*0.03*t, 0.5)+50*cos(2*pi*0.1*t);
%  sxo4=sxo3+50* sawtooth(2*pi*0.03*t, 0.5)+30*cos(2*pi*0.1*t);
%   figure;
%  plot3(sxo3,syo4,szo3,'ro');
% for i=1:length(syo)
% if (sxo(i)>100&&syo(i)>50)|(sxo(i)<-100&&syo(i)>50)|(sxo(i)>100&&syo(i)<-50)|(sxo(i)<-100&&syo(i)<-50)
%     sxo2(i)=-4.55;
%     syo2(i)=-5.5;
% else
%     sxo2(i)=sxo(i);
%     syo2(i)=syo(i);
% end
% end

%  xlabel('x');
%  ylabel('y');
 %% 

% t=(0:40/length(sxo):40-40/length(sxo));
% %  syo4=syo+20*sin(2*pi*0.05*t)+20.*cos(2*pi*0.1*t)+20*cos(0.1*t)+100 * sawtooth(2*pi*0.02*t, 0.5);
% % figure;
% %  plot3(sxo,syo4,szo,'ro');
% %  view(2);
% for i=1:100
%  % syo4=syo+1*i*sin(2*pi*0.2*t);
%  syo4=syo3+1*i * sawtooth(2*pi*0.03*t, 0.5)+0.5*i.*cos(2*pi*0.1*t);
%  sxo4=sxo3+1*i * sawtooth(2*pi*0.03*t, 0.5)+0.5*i.*cos(2*pi*0.1*t);
%  clf;
%  plot3(syo4,sxo,szo,'ro');
%    view(2);
%  drawnow;  
% end
 % end

